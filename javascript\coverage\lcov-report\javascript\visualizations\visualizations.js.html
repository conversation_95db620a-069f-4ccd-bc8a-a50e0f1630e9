
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for javascript/visualizations/visualizations.js</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">javascript/visualizations</a> visualizations.js</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/199</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/40</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/36</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/182</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">/**
 * Algorithm Visualization System
 * Interactive demonstrations of various algorithms
 */
&nbsp;
class AlgorithmVisualizer {
<span class="fstat-no" title="function not covered" >    co</span>nstructor() {
<span class="cstat-no" title="statement not covered" >        this.canvas = null;</span>
<span class="cstat-no" title="statement not covered" >        this.ctx = null;</span>
<span class="cstat-no" title="statement not covered" >        this.currentAlgorithm = null;</span>
<span class="cstat-no" title="statement not covered" >        this.isRunning = false;</span>
<span class="cstat-no" title="statement not covered" >        this.isPaused = false;</span>
<span class="cstat-no" title="statement not covered" >        this.animationSpeed = 5;</span>
<span class="cstat-no" title="statement not covered" >        this.stats = {</span>
            comparisons: 0,
            swaps: 0,
            arrayAccess: 0,
            startTime: 0
        };
        
        // Algorithm data
<span class="cstat-no" title="statement not covered" >        this.array = [];</span>
<span class="cstat-no" title="statement not covered" >        this.arraySize = 50;</span>
<span class="cstat-no" title="statement not covered" >        this.currentStep = 0;</span>
<span class="cstat-no" title="statement not covered" >        this.steps = [];</span>
        
        // Colors
<span class="cstat-no" title="statement not covered" >        this.colors = {</span>
            default: '#3498db',
            comparing: '#e74c3c',
            swapping: '#f39c12',
            sorted: '#2ecc71',
            pivot: '#9b59b6',
            current: '#e67e22'
        };
        
<span class="cstat-no" title="statement not covered" >        this.algorithms = {</span>
            'bubble-sort': {
                name: 'Bubble Sort',
                description: 'Bubble Sort repeatedly steps through the list, compares adjacent elements and swaps them if they are in the wrong order.',
                timeComplexity: 'O(n²)',
                spaceComplexity: 'O(1)',
                visualize: this.visualizeBubbleSort.bind(this)
            },
            'quick-sort': {
                name: 'Quick Sort',
                description: 'Quick Sort picks a pivot element and partitions the array around it, then recursively sorts the sub-arrays.',
                timeComplexity: 'O(n log n) average, O(n²) worst',
                spaceComplexity: 'O(log n)',
                visualize: this.visualizeQuickSort.bind(this)
            },
            'binary-search': {
                name: 'Binary Search',
                description: 'Binary Search finds a target value in a sorted array by repeatedly dividing the search interval in half.',
                timeComplexity: 'O(log n)',
                spaceComplexity: 'O(1)',
                visualize: this.visualizeBinarySearch.bind(this)
            },
            'linear-regression': {
                name: 'Linear Regression',
                description: 'Linear Regression finds the best fit line through data points using least squares method.',
                timeComplexity: 'O(n)',
                spaceComplexity: 'O(1)',
                visualize: this.visualizeLinearRegression.bind(this)
            }
        };
    }
    
<span class="fstat-no" title="function not covered" >    in</span>it() {
<span class="cstat-no" title="statement not covered" >        this.canvas = document.getElementById('visualizationCanvas');</span>
<span class="cstat-no" title="statement not covered" >        this.ctx = this.canvas.getContext('2d');</span>
        
<span class="cstat-no" title="statement not covered" >        this.setupEventListeners();</span>
<span class="cstat-no" title="statement not covered" >        this.generateRandomArray();</span>
<span class="cstat-no" title="statement not covered" >        this.draw();</span>
    }
    
<span class="fstat-no" title="function not covered" >    se</span>tupEventListeners() {
        // Algorithm selection
<span class="cstat-no" title="statement not covered" >        document.querySelectorAll('.algorithm-card').forEach(<span class="fstat-no" title="function not covered" >ca</span>rd =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            card.addEventListener('click', <span class="fstat-no" title="function not covered" >(e</span>) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                this.selectAlgorithm(card.dataset.algorithm);</span>
            });
        });
        
        // Control buttons
<span class="cstat-no" title="statement not covered" >        document.getElementById('startBtn').addEventListener('click', <span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >this.start())</span>;</span>
<span class="cstat-no" title="statement not covered" >        document.getElementById('pauseBtn').addEventListener('click', <span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >this.pause())</span>;</span>
<span class="cstat-no" title="statement not covered" >        document.getElementById('resetBtn').addEventListener('click', <span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >this.reset())</span>;</span>
<span class="cstat-no" title="statement not covered" >        document.getElementById('randomizeBtn').addEventListener('click', <span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >this.randomize())</span>;</span>
        
        // Speed control
<span class="cstat-no" title="statement not covered" >        document.getElementById('speedSlider').addEventListener('input', <span class="fstat-no" title="function not covered" >(e</span>) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            this.animationSpeed = parseInt(e.target.value);</span>
        });
    }
    
<span class="fstat-no" title="function not covered" >    se</span>lectAlgorithm(algorithmKey) {
        // Update UI
<span class="cstat-no" title="statement not covered" >        document.querySelectorAll('.algorithm-card').forEach(<span class="fstat-no" title="function not covered" >ca</span>rd =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            card.classList.remove('active');</span>
        });
<span class="cstat-no" title="statement not covered" >        document.querySelector(`[data-algorithm="${algorithmKey}"]`).classList.add('active');</span>
        
        // Update algorithm info
        const algorithm = <span class="cstat-no" title="statement not covered" >this.algorithms[algorithmKey];</span>
<span class="cstat-no" title="statement not covered" >        if (algorithm) {</span>
<span class="cstat-no" title="statement not covered" >            this.currentAlgorithm = algorithmKey;</span>
<span class="cstat-no" title="statement not covered" >            document.getElementById('algorithmTitle').textContent = algorithm.name;</span>
<span class="cstat-no" title="statement not covered" >            document.getElementById('algorithmDescription').textContent = algorithm.description;</span>
<span class="cstat-no" title="statement not covered" >            document.getElementById('timeComplexity').textContent = algorithm.timeComplexity;</span>
<span class="cstat-no" title="statement not covered" >            document.getElementById('spaceComplexity').textContent = algorithm.spaceComplexity;</span>
        }
        
<span class="cstat-no" title="statement not covered" >        this.reset();</span>
    }
    
<span class="fstat-no" title="function not covered" >    ge</span>nerateRandomArray() {
<span class="cstat-no" title="statement not covered" >        this.array = [];</span>
<span class="cstat-no" title="statement not covered" >        for (let i = <span class="cstat-no" title="statement not covered" >0;</span> i &lt; this.arraySize; i++) {</span>
<span class="cstat-no" title="statement not covered" >            this.array.push(Math.floor(Math.random() * 300) + 10);</span>
        }
    }
    
<span class="fstat-no" title="function not covered" >    st</span>art() {
<span class="cstat-no" title="statement not covered" >        if (!this.currentAlgorithm) {</span>
<span class="cstat-no" title="statement not covered" >            alert('Please select an algorithm first!');</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
        
<span class="cstat-no" title="statement not covered" >        if (this.isPaused) {</span>
<span class="cstat-no" title="statement not covered" >            this.isPaused = false;</span>
<span class="cstat-no" title="statement not covered" >            this.animate();</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
        
<span class="cstat-no" title="statement not covered" >        this.isRunning = true;</span>
<span class="cstat-no" title="statement not covered" >        this.stats.startTime = Date.now();</span>
<span class="cstat-no" title="statement not covered" >        this.resetStats();</span>
        
        // Generate steps for the selected algorithm
<span class="cstat-no" title="statement not covered" >        this.algorithms[this.currentAlgorithm].visualize();</span>
<span class="cstat-no" title="statement not covered" >        this.animate();</span>
    }
    
<span class="fstat-no" title="function not covered" >    pa</span>use() {
<span class="cstat-no" title="statement not covered" >        this.isPaused = !this.isPaused;</span>
<span class="cstat-no" title="statement not covered" >        document.getElementById('pauseBtn').textContent = this.isPaused ? '▶️ Resume' : '⏸️ Pause';</span>
    }
    
<span class="fstat-no" title="function not covered" >    re</span>set() {
<span class="cstat-no" title="statement not covered" >        this.isRunning = false;</span>
<span class="cstat-no" title="statement not covered" >        this.isPaused = false;</span>
<span class="cstat-no" title="statement not covered" >        this.currentStep = 0;</span>
<span class="cstat-no" title="statement not covered" >        this.steps = [];</span>
<span class="cstat-no" title="statement not covered" >        this.resetStats();</span>
<span class="cstat-no" title="statement not covered" >        document.getElementById('pauseBtn').textContent = '⏸️ Pause';</span>
<span class="cstat-no" title="statement not covered" >        this.draw();</span>
    }
    
<span class="fstat-no" title="function not covered" >    ra</span>ndomize() {
<span class="cstat-no" title="statement not covered" >        this.generateRandomArray();</span>
<span class="cstat-no" title="statement not covered" >        this.reset();</span>
    }
    
<span class="fstat-no" title="function not covered" >    re</span>setStats() {
<span class="cstat-no" title="statement not covered" >        this.stats.comparisons = 0;</span>
<span class="cstat-no" title="statement not covered" >        this.stats.swaps = 0;</span>
<span class="cstat-no" title="statement not covered" >        this.stats.arrayAccess = 0;</span>
<span class="cstat-no" title="statement not covered" >        this.updateStatsDisplay();</span>
    }
    
<span class="fstat-no" title="function not covered" >    up</span>dateStatsDisplay() {
<span class="cstat-no" title="statement not covered" >        document.getElementById('comparisons').textContent = this.stats.comparisons;</span>
<span class="cstat-no" title="statement not covered" >        document.getElementById('swaps').textContent = this.stats.swaps;</span>
<span class="cstat-no" title="statement not covered" >        document.getElementById('arrayAccess').textContent = this.stats.arrayAccess;</span>
        
<span class="cstat-no" title="statement not covered" >        if (this.stats.startTime) {</span>
            const elapsed = <span class="cstat-no" title="statement not covered" >Date.now() - this.stats.startTime;</span>
<span class="cstat-no" title="statement not covered" >            document.getElementById('timeElapsed').textContent = elapsed + 'ms';</span>
        }
    }
    
<span class="fstat-no" title="function not covered" >    an</span>imate() {
<span class="cstat-no" title="statement not covered" >        if (!this.isRunning || this.isPaused) <span class="cstat-no" title="statement not covered" >return;</span></span>
        
<span class="cstat-no" title="statement not covered" >        if (this.currentStep &lt; this.steps.length) {</span>
            const step = <span class="cstat-no" title="statement not covered" >this.steps[this.currentStep];</span>
<span class="cstat-no" title="statement not covered" >            this.executeStep(step);</span>
<span class="cstat-no" title="statement not covered" >            this.draw();</span>
<span class="cstat-no" title="statement not covered" >            this.updateStatsDisplay();</span>
<span class="cstat-no" title="statement not covered" >            this.currentStep++;</span>
            
<span class="cstat-no" title="statement not covered" >            setTimeout(<span class="fstat-no" title="function not covered" >()</span> =&gt; <span class="cstat-no" title="statement not covered" >this.animate(),</span> 1000 / this.animationSpeed);</span>
        } else {
<span class="cstat-no" title="statement not covered" >            this.isRunning = false;</span>
        }
    }
    
<span class="fstat-no" title="function not covered" >    ex</span>ecuteStep(step) {
<span class="cstat-no" title="statement not covered" >        switch (step.type) {</span>
            case 'compare':
<span class="cstat-no" title="statement not covered" >                this.stats.comparisons++;</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            case 'swap':
<span class="cstat-no" title="statement not covered" >                this.stats.swaps++;</span>
<span class="cstat-no" title="statement not covered" >                [this.array[step.i], this.array[step.j]] = [this.array[step.j], this.array[step.i]];</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            case 'access':
<span class="cstat-no" title="statement not covered" >                this.stats.arrayAccess++;</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
        }
    }
    
<span class="fstat-no" title="function not covered" >    dr</span>aw() {
<span class="cstat-no" title="statement not covered" >        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);</span>
        
<span class="cstat-no" title="statement not covered" >        if (this.currentAlgorithm === 'linear-regression') {</span>
<span class="cstat-no" title="statement not covered" >            this.drawLinearRegression();</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
        
        const barWidth = <span class="cstat-no" title="statement not covered" >this.canvas.width / this.array.length;</span>
        const maxHeight = <span class="cstat-no" title="statement not covered" >this.canvas.height - 50;</span>
        const maxValue = <span class="cstat-no" title="statement not covered" >Math.max(...this.array);</span>
        
<span class="cstat-no" title="statement not covered" >        for (let i = <span class="cstat-no" title="statement not covered" >0;</span> i &lt; this.array.length; i++) {</span>
            const height = <span class="cstat-no" title="statement not covered" >(this.array[i] / maxValue) * maxHeight;</span>
            const x = <span class="cstat-no" title="statement not covered" >i * barWidth;</span>
            const y = <span class="cstat-no" title="statement not covered" >this.canvas.height - height;</span>
            
            // Determine color based on current step
            let color = <span class="cstat-no" title="statement not covered" >this.colors.default;</span>
<span class="cstat-no" title="statement not covered" >            if (this.currentStep &lt; this.steps.length) {</span>
                const step = <span class="cstat-no" title="statement not covered" >this.steps[this.currentStep];</span>
<span class="cstat-no" title="statement not covered" >                if (step &amp;&amp; (step.i === i || step.j === i)) {</span>
<span class="cstat-no" title="statement not covered" >                    color = step.type === 'compare' ? this.colors.comparing : this.colors.swapping;</span>
                }
            }
            
<span class="cstat-no" title="statement not covered" >            this.ctx.fillStyle = color;</span>
<span class="cstat-no" title="statement not covered" >            this.ctx.fillRect(x, y, barWidth - 1, height);</span>
            
            // Draw value on top of bar
<span class="cstat-no" title="statement not covered" >            this.ctx.fillStyle = '#2c3e50';</span>
<span class="cstat-no" title="statement not covered" >            this.ctx.font = '10px Arial';</span>
<span class="cstat-no" title="statement not covered" >            this.ctx.textAlign = 'center';</span>
<span class="cstat-no" title="statement not covered" >            this.ctx.fillText(this.array[i], x + barWidth/2, y - 5);</span>
        }
    }
    
<span class="fstat-no" title="function not covered" >    dr</span>awLinearRegression() {
        // Generate sample data points
        const points = <span class="cstat-no" title="statement not covered" >[];</span>
<span class="cstat-no" title="statement not covered" >        for (let i = <span class="cstat-no" title="statement not covered" >0;</span> i &lt; 20; i++) {</span>
            const x = <span class="cstat-no" title="statement not covered" >(i / 19) * this.canvas.width;</span>
            const y = <span class="cstat-no" title="statement not covered" >this.canvas.height/2 + (Math.random() - 0.5) * 200 + (i - 10) * 5;</span>
<span class="cstat-no" title="statement not covered" >            points.push({x, y});</span>
        }
        
        // Draw points
<span class="cstat-no" title="statement not covered" >        this.ctx.fillStyle = this.colors.comparing;</span>
<span class="cstat-no" title="statement not covered" >        points.forEach(<span class="fstat-no" title="function not covered" >po</span>int =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            this.ctx.beginPath();</span>
<span class="cstat-no" title="statement not covered" >            this.ctx.arc(point.x, point.y, 4, 0, 2 * Math.PI);</span>
<span class="cstat-no" title="statement not covered" >            this.ctx.fill();</span>
        });
        
        // Calculate and draw regression line
        const n = <span class="cstat-no" title="statement not covered" >points.length;</span>
        const sumX = <span class="cstat-no" title="statement not covered" >points.reduce(<span class="fstat-no" title="function not covered" >(s</span>um, p) =&gt; <span class="cstat-no" title="statement not covered" >sum + p.x,</span> 0);</span>
        const sumY = <span class="cstat-no" title="statement not covered" >points.reduce(<span class="fstat-no" title="function not covered" >(s</span>um, p) =&gt; <span class="cstat-no" title="statement not covered" >sum + p.y,</span> 0);</span>
        const sumXY = <span class="cstat-no" title="statement not covered" >points.reduce(<span class="fstat-no" title="function not covered" >(s</span>um, p) =&gt; <span class="cstat-no" title="statement not covered" >sum + p.x * p.y,</span> 0);</span>
        const sumXX = <span class="cstat-no" title="statement not covered" >points.reduce(<span class="fstat-no" title="function not covered" >(s</span>um, p) =&gt; <span class="cstat-no" title="statement not covered" >sum + p.x * p.x,</span> 0);</span>
        
        const slope = <span class="cstat-no" title="statement not covered" >(n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);</span>
        const intercept = <span class="cstat-no" title="statement not covered" >(sumY - slope * sumX) / n;</span>
        
        // Draw regression line
<span class="cstat-no" title="statement not covered" >        this.ctx.strokeStyle = this.colors.sorted;</span>
<span class="cstat-no" title="statement not covered" >        this.ctx.lineWidth = 3;</span>
<span class="cstat-no" title="statement not covered" >        this.ctx.beginPath();</span>
<span class="cstat-no" title="statement not covered" >        this.ctx.moveTo(0, intercept);</span>
<span class="cstat-no" title="statement not covered" >        this.ctx.lineTo(this.canvas.width, slope * this.canvas.width + intercept);</span>
<span class="cstat-no" title="statement not covered" >        this.ctx.stroke();</span>
        
        // Draw equation
<span class="cstat-no" title="statement not covered" >        this.ctx.fillStyle = '#2c3e50';</span>
<span class="cstat-no" title="statement not covered" >        this.ctx.font = '16px Arial';</span>
<span class="cstat-no" title="statement not covered" >        this.ctx.fillText(`y = ${slope.toFixed(2)}x + ${intercept.toFixed(2)}`, 20, 30);</span>
    }
    
    // Algorithm implementations
<span class="fstat-no" title="function not covered" >    vi</span>sualizeBubbleSort() {
<span class="cstat-no" title="statement not covered" >        this.steps = [];</span>
        const arr = <span class="cstat-no" title="statement not covered" >[...this.array];</span>
        
<span class="cstat-no" title="statement not covered" >        for (let i = <span class="cstat-no" title="statement not covered" >0;</span> i &lt; arr.length - 1; i++) {</span>
<span class="cstat-no" title="statement not covered" >            for (let j = <span class="cstat-no" title="statement not covered" >0;</span> j &lt; arr.length - i - 1; j++) {</span>
<span class="cstat-no" title="statement not covered" >                this.steps.push({type: 'compare', i: j, j: j + 1});</span>
<span class="cstat-no" title="statement not covered" >                if (arr[j] &gt; arr[j + 1]) {</span>
<span class="cstat-no" title="statement not covered" >                    this.steps.push({type: 'swap', i: j, j: j + 1});</span>
<span class="cstat-no" title="statement not covered" >                    [arr[j], arr[j + 1]] = [arr[j + 1], arr[j]];</span>
                }
            }
        }
    }
    
<span class="fstat-no" title="function not covered" >    vi</span>sualizeQuickSort() {
<span class="cstat-no" title="statement not covered" >        this.steps = [];</span>
        const arr = <span class="cstat-no" title="statement not covered" >[...this.array];</span>
        
        const quickSort = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(l</span>ow, high) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            if (low &lt; high) {</span>
                const pi = <span class="cstat-no" title="statement not covered" >partition(low, high);</span>
<span class="cstat-no" title="statement not covered" >                quickSort(low, pi - 1);</span>
<span class="cstat-no" title="statement not covered" >                quickSort(pi + 1, high);</span>
            }
        };
        
        const partition = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(l</span>ow, high) =&gt; {</span>
            const pivot = <span class="cstat-no" title="statement not covered" >arr[high];</span>
            let i = <span class="cstat-no" title="statement not covered" >low - 1;</span>
            
<span class="cstat-no" title="statement not covered" >            for (let j = <span class="cstat-no" title="statement not covered" >low;</span> j &lt; high; j++) {</span>
<span class="cstat-no" title="statement not covered" >                this.steps.push({type: 'compare', i: j, j: high});</span>
<span class="cstat-no" title="statement not covered" >                if (arr[j] &lt; pivot) {</span>
<span class="cstat-no" title="statement not covered" >                    i++;</span>
<span class="cstat-no" title="statement not covered" >                    this.steps.push({type: 'swap', i: i, j: j});</span>
<span class="cstat-no" title="statement not covered" >                    [arr[i], arr[j]] = [arr[j], arr[i]];</span>
                }
            }
            
<span class="cstat-no" title="statement not covered" >            this.steps.push({type: 'swap', i: i + 1, j: high});</span>
<span class="cstat-no" title="statement not covered" >            [arr[i + 1], arr[high]] = [arr[high], arr[i + 1]];</span>
<span class="cstat-no" title="statement not covered" >            return i + 1;</span>
        };
        
<span class="cstat-no" title="statement not covered" >        quickSort(0, arr.length - 1);</span>
    }
    
<span class="fstat-no" title="function not covered" >    vi</span>sualizeBinarySearch() {
<span class="cstat-no" title="statement not covered" >        this.steps = [];</span>
        // First sort the array for binary search
<span class="cstat-no" title="statement not covered" >        this.array.sort(<span class="fstat-no" title="function not covered" >(a</span>, b) =&gt; <span class="cstat-no" title="statement not covered" >a - b)</span>;</span>
        const target = <span class="cstat-no" title="statement not covered" >this.array[Math.floor(Math.random() * this.array.length)];</span>
        
        let left = <span class="cstat-no" title="statement not covered" >0;</span>
        let right = <span class="cstat-no" title="statement not covered" >this.array.length - 1;</span>
        
<span class="cstat-no" title="statement not covered" >        while (left &lt;= right) {</span>
            const mid = <span class="cstat-no" title="statement not covered" >Math.floor((left + right) / 2);</span>
<span class="cstat-no" title="statement not covered" >            this.steps.push({type: 'compare', i: mid, j: mid, target});</span>
            
<span class="cstat-no" title="statement not covered" >            if (this.array[mid] === target) {</span>
<span class="cstat-no" title="statement not covered" >                this.steps.push({type: 'found', i: mid});</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            } else <span class="cstat-no" title="statement not covered" >if (this.array[mid] &lt; target) {</span>
<span class="cstat-no" title="statement not covered" >                left = mid + 1;</span>
            } else {
<span class="cstat-no" title="statement not covered" >                right = mid - 1;</span>
            }
        }
    }
    
<span class="fstat-no" title="function not covered" >    vi</span>sualizeLinearRegression() {
<span class="cstat-no" title="statement not covered" >        this.steps = [];</span>
        // This will be handled in the draw function
    }
}
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-28T19:22:35.733Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    