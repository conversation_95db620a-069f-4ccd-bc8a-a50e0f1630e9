{"name": "algorithms-collection-js", "version": "1.0.0", "description": "Comprehensive collection of algorithms and data structures implemented in JavaScript", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:verbose": "jest --verbose", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "echo 'Skipping husky install'"}, "keywords": ["algorithms", "data-structures", "javascript", "computer-science", "interview-preparation", "leetcode", "sorting", "searching", "trees", "graphs"], "author": "Algorithms Collection Contributors", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/algorithms.git"}, "bugs": {"url": "https://github.com/your-username/algorithms/issues"}, "homepage": "https://github.com/your-username/algorithms#readme", "dependencies": {}, "devDependencies": {"jest": "^29.7.0", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-jest": "^27.4.0", "prettier": "^3.0.3", "husky": "^8.0.3", "lint-staged": "^14.0.1"}, "jest": {"testEnvironment": "node", "collectCoverageFrom": ["**/*.js", "!**/node_modules/**", "!**/coverage/**", "!**/.eslintrc.js", "!**/.prettierrc.js", "!**/jest.config.js"], "coverageThreshold": {"global": {"branches": 75, "functions": 75, "lines": 75, "statements": 75}}, "coverageReporters": ["text", "lcov", "html"]}, "lint-staged": {"*.js": ["eslint --fix", "prettier --write"]}}