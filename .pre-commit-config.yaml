# Pre-commit hooks configuration
# See https://pre-commit.com for more information

repos:
  # General hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
        exclude: ^.*\.md$
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: debug-statements
      - id: detect-private-key
      - id: mixed-line-ending
        args: ['--fix=lf']

  # Python hooks
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        language_version: python3
        files: ^python/.*\.py$

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        files: ^python/.*\.py$
        args: ["--profile", "black"]

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        files: ^python/.*\.py$
        args: [--max-line-length=88, --extend-ignore=E203,W503]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.5.1
    hooks:
      - id: mypy
        files: ^python/.*\.py$
        additional_dependencies: [types-all]
        args: [--ignore-missing-imports]

  # JavaScript/Node.js hooks
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.47.0
    hooks:
      - id: eslint
        files: ^javascript/.*\.(js|jsx)$
        additional_dependencies:
          - eslint@8.47.0
          - eslint-config-prettier@9.0.0
          - eslint-plugin-jest@27.2.3

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.1
    hooks:
      - id: prettier
        files: ^javascript/.*\.(js|jsx|json|md)$
        additional_dependencies:
          - prettier@3.0.1

  # Documentation hooks
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.35.0
    hooks:
      - id: markdownlint
        args: [--fix]
        exclude: ^(CHANGELOG\.md|node_modules/)

  # Security hooks
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline']
        exclude: package.lock.json

  # Commit message hooks
  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.6.0
    hooks:
      - id: commitizen
        stages: [commit-msg]

# Configuration for specific hooks
default_language_version:
  python: python3.8
  node: 18.17.0

# Global excludes
exclude: |
  (?x)^(
    .*\.min\.js|
    .*\.min\.css|
    node_modules/|
    \.git/|
    \.pytest_cache/|
    __pycache__/|
    \.mypy_cache/|
    coverage/|
    dist/|
    build/|
    \.venv/|
    venv/
  )$

# Fail fast - stop on first failure
fail_fast: false

# Minimum pre-commit version
minimum_pre_commit_version: 3.0.0
