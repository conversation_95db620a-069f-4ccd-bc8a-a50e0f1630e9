{"algorithms": {"strings": {"category": "Алгоритми для рядків", "description": "Алгоритми для маніпуляції та аналізу рядків", "anagrams": {"name": "Анагра<PERSON>и", "description": "Перевірити, чи є два рядки анаграмами один одного"}, "palindrome": {"name": "Паліндром", "description": "Перевірити, чи читається рядок однаково в обох напрямках"}, "reversestring": {"name": "Обернути рядок", "description": "Обернути символи в рядку"}, "capitalize": {"name": "Капітал<PERSON>зація", "description": "Зробити першу літеру кожного слова великою"}, "maxchar": {"name": "Максимальний символ", "description": "Знайти найчастіше використовуваний символ у рядку"}, "vowels": {"name": "Підрахунок голосних", "description": "Підрахувати кількість голосних у рядку"}}, "math": {"category": "Математичні алгоритми", "description": "Алгоритми для математичних обчислень та теорії чисел", "fib": {"name": "Фібоначчі", "description": "Генерувати числа послідовності Фібоначчі"}, "fizzbuzz": {"name": "FizzBuzz", "description": "Класичний програмістський виклик з правилами подільності"}, "reverseint": {"name": "Обернути число", "description": "Обернути цифри цілого числа"}, "steps": {"name": "Патерн сходинок", "description": "Надрукувати патерн сходинок використовуючи символи решітки"}, "pyramid": {"name": "Патерн піраміди", "description": "Надрукувати патерн піраміди використовуючи символи решітки"}}, "sorting": {"category": "Алгоритми сортування", "description": "Алгоритми для впорядкування даних", "bubbleSort": {"name": "Бульбашкове сортування", "description": "Простий алгоритм сортування, який повторно проходить через список"}, "selectionSort": {"name": "Сортування вибором", "description": "Алгоритм сортування, який повторно вибирає мінімальний елемент"}, "mergeSort": {"name": "Сортування злиттям", "description": "Алгоритм сортування \"розділяй і володарюй\""}, "quickSort": {"name": "Швидке сортування", "description": "Ефективний алгоритм сортування \"розділяй і володарюй\""}, "heapSort": {"name": "Пірамідальне сортування", "description": "Сортування на основі порівняння з використанням структури бінарної купи"}}, "search": {"category": "Алгоритми пошуку", "description": "Алгоритми для знаходження елементів у структурах даних", "binarySearch": {"name": "Бінарний пошук", "description": "Ефективний алгоритм пошуку для відсортованих масивів"}, "binarySearchRecursive": {"name": "Бінар<PERSON>ий пошук (рекурсивний)", "description": "Рекурсивна реалізація бінарного пошуку"}, "searchRotatedArray": {"name": "Пошук у повернутому масиві", "description": "Пошук у повернутому відсортованому масиві"}}, "graphs": {"category": "Алгоритми графів", "description": "Алгоритми для обходу графів та пошуку шляхів", "dijkstra": {"name": "Алгоритм Дейкстри", "description": "Знаходження найкоротших шляхів у зважених графах"}, "astar": {"name": "A* пошук шляху", "description": "Алгоритм пошуку шляху на основі евристики"}, "shortestPath": {"name": "Найкорот<PERSON>ий шлях", "description": "Знайти найкоротший шлях між двома вузлами"}}, "machineLearning": {"category": "Машинне навчання", "description": "Базові алгоритми та моделі машинного навчання", "LinearRegression": {"name": "Лін<PERSON>йна регресія", "description": "Знайти найкращу лінію, що проходить через точки даних"}, "KMeans": {"name": "K-середніх кластеризація", "description": "Розділити дані на k кластерів"}, "KNearestNeighbors": {"name": "K найближчих сусідів", "description": "Класифікувати дані на основі найближчих сусідів"}}}, "complexity": {"time": "Часова складність", "space": "Просторова складність", "best": "Найкращий випадок", "average": "Середній випадок", "worst": "Найг<PERSON><PERSON><PERSON>ий випадок"}, "ui": {"start": "Почати", "pause": "Пауза", "resume": "Продовжити", "reset": "Скинути", "randomize": "Випадково", "speed": "Швидкість", "comparisons": "Порівняння", "swaps": "Об<PERSON><PERSON><PERSON>и", "arrayAccess": "Доступ до масиву", "timeElapsed": "Час виконання", "selectAlgorithm": "Виберіть алгоритм", "algorithmInfo": "Інформація про алгоритм", "visualization": "Візуалізація", "examples": "Приклади", "usage": "Використання"}, "messages": {"selectAlgorithmFirst": "Будь ласка, спочатку виберіть алгоритм!", "algorithmCompleted": "Алгоритм успішно завершено!", "noPathFound": "Шлях між вказаними точками не знайдено", "invalidInput": "Надано неправильні вхідні дані", "trainingRequired": "Модель повинна бути навчена перед прогнозуванням"}}