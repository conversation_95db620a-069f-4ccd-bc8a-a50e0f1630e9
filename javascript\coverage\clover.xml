<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1748458138682" clover="3.2.0">
  <project timestamp="1748458138682" name="All files">
    <metrics statements="669" coveredstatements="403" conditionals="244" coveredconditionals="160" methods="129" coveredmethods="93" elements="1042" coveredelements="656" complexity="0" loc="669" ncloc="669" packages="28" files="37" classes="37"/>
    <package name="javascript">
      <metrics statements="52" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name=".eslintrc.js" path="D:\AI\dev\Algorithms\javascript\.eslintrc.js">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
      </file>
      <file name=".prettierrc.js" path="D:\AI\dev\Algorithms\javascript\.prettierrc.js">
        <metrics statements="1" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
      </file>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\index.js">
        <metrics statements="50" coveredstatements="0" conditionals="4" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="191" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
      </file>
    </package>
    <package name="javascript.anagrams">
      <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\anagrams\index.js">
        <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="12" count="5" type="stmt"/>
        <line num="16" count="10" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.bst">
      <metrics statements="19" coveredstatements="19" conditionals="22" coveredconditionals="21" methods="3" coveredmethods="3"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\bst\index.js">
        <metrics statements="19" coveredstatements="19" conditionals="22" coveredconditionals="21" methods="3" coveredmethods="3"/>
        <line num="17" count="18" type="stmt"/>
        <line num="18" count="18" type="stmt"/>
        <line num="19" count="18" type="stmt"/>
        <line num="23" count="28" type="cond" truecount="4" falsecount="0"/>
        <line num="24" count="10" type="stmt"/>
        <line num="25" count="18" type="cond" truecount="2" falsecount="0"/>
        <line num="26" count="7" type="stmt"/>
        <line num="27" count="11" type="cond" truecount="4" falsecount="0"/>
        <line num="28" count="3" type="stmt"/>
        <line num="29" count="8" type="cond" truecount="1" falsecount="1"/>
        <line num="30" count="8" type="stmt"/>
        <line num="35" count="7" type="cond" truecount="2" falsecount="0"/>
        <line num="36" count="1" type="stmt"/>
        <line num="39" count="6" type="cond" truecount="4" falsecount="0"/>
        <line num="40" count="3" type="stmt"/>
        <line num="41" count="3" type="cond" truecount="4" falsecount="0"/>
        <line num="42" count="2" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.capitalize">
      <metrics statements="7" coveredstatements="7" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\capitalize\index.js">
        <metrics statements="7" coveredstatements="7" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
        <line num="11" count="2" type="stmt"/>
        <line num="13" count="2" type="stmt"/>
        <line num="14" count="59" type="cond" truecount="2" falsecount="0"/>
        <line num="15" count="11" type="stmt"/>
        <line num="17" count="48" type="stmt"/>
        <line num="21" count="2" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.chunk">
      <metrics statements="7" coveredstatements="7" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\chunk\index.js">
        <metrics statements="7" coveredstatements="7" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="12" count="4" type="stmt"/>
        <line num="13" count="4" type="stmt"/>
        <line num="15" count="4" type="stmt"/>
        <line num="16" count="13" type="stmt"/>
        <line num="17" count="13" type="stmt"/>
        <line num="20" count="4" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.circular">
      <metrics statements="98" coveredstatements="15" conditionals="34" coveredconditionals="6" methods="16" coveredmethods="4"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\circular\index.js">
        <metrics statements="9" coveredstatements="9" conditionals="4" coveredconditionals="4" methods="1" coveredmethods="1"/>
        <line num="16" count="3" type="stmt"/>
        <line num="17" count="3" type="stmt"/>
        <line num="19" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="20" count="6" type="stmt"/>
        <line num="21" count="6" type="stmt"/>
        <line num="23" count="6" type="cond" truecount="2" falsecount="0"/>
        <line num="24" count="2" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
      </file>
      <file name="linkedlist.js" path="D:\AI\dev\Algorithms\javascript\circular\linkedlist.js">
        <metrics statements="89" coveredstatements="6" conditionals="30" coveredconditionals="2" methods="15" coveredmethods="3"/>
        <line num="3" count="9" type="stmt"/>
        <line num="4" count="9" type="stmt"/>
        <line num="10" count="3" type="stmt"/>
        <line num="12" count="3" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="125" count="6" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="159" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.events">
      <metrics statements="9" coveredstatements="9" conditionals="4" coveredconditionals="4" methods="4" coveredmethods="4"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\events\index.js">
        <metrics statements="9" coveredstatements="9" conditionals="4" coveredconditionals="4" methods="4" coveredmethods="4"/>
        <line num="8" count="5" type="stmt"/>
        <line num="13" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="14" count="2" type="stmt"/>
        <line num="16" count="7" type="stmt"/>
        <line num="23" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="24" count="10" type="stmt"/>
        <line num="25" count="13" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.fib">
      <metrics statements="12" coveredstatements="12" conditionals="4" coveredconditionals="4" methods="3" coveredmethods="3"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\fib\index.js">
        <metrics statements="12" coveredstatements="12" conditionals="4" coveredconditionals="4" methods="3" coveredmethods="3"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="81" type="cond" truecount="2" falsecount="0"/>
        <line num="15" count="41" type="stmt"/>
        <line num="18" count="40" type="stmt"/>
        <line num="19" count="40" type="stmt"/>
        <line num="21" count="40" type="stmt"/>
        <line num="26" count="40" type="cond" truecount="2" falsecount="0"/>
        <line num="27" count="2" type="stmt"/>
        <line num="30" count="38" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.fizzbuzz">
      <metrics statements="9" coveredstatements="9" conditionals="8" coveredconditionals="8" methods="1" coveredmethods="1"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\fizzbuzz\index.js">
        <metrics statements="9" coveredstatements="9" conditionals="8" coveredconditionals="8" methods="1" coveredmethods="1"/>
        <line num="16" count="2" type="stmt"/>
        <line num="18" count="20" type="cond" truecount="4" falsecount="0"/>
        <line num="19" count="1" type="stmt"/>
        <line num="20" count="19" type="cond" truecount="2" falsecount="0"/>
        <line num="22" count="5" type="stmt"/>
        <line num="23" count="14" type="cond" truecount="2" falsecount="0"/>
        <line num="24" count="3" type="stmt"/>
        <line num="26" count="11" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.fromlast">
      <metrics statements="84" coveredstatements="26" conditionals="29" coveredconditionals="7" methods="16" coveredmethods="6"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\fromlast\index.js">
        <metrics statements="10" coveredstatements="10" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="3" type="stmt"/>
        <line num="20" count="3" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
      </file>
      <file name="linkedlist.js" path="D:\AI\dev\Algorithms\javascript\fromlast\linkedlist.js">
        <metrics statements="74" coveredstatements="16" conditionals="29" coveredconditionals="7" methods="15" coveredmethods="5"/>
        <line num="3" count="5" type="stmt"/>
        <line num="4" count="5" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="30" count="2" type="stmt"/>
        <line num="34" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="35" count="1" type="stmt"/>
        <line num="38" count="4" type="stmt"/>
        <line num="39" count="4" type="stmt"/>
        <line num="40" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="41" count="4" type="stmt"/>
        <line num="43" count="6" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="79" count="5" type="stmt"/>
        <line num="81" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="83" count="4" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="116" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="127" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.levelwidth">
      <metrics statements="15" coveredstatements="15" conditionals="2" coveredconditionals="2" methods="3" coveredmethods="3"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\levelwidth\index.js">
        <metrics statements="11" coveredstatements="11" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
        <line num="15" count="2" type="stmt"/>
        <line num="16" count="2" type="stmt"/>
        <line num="18" count="2" type="stmt"/>
        <line num="19" count="16" type="stmt"/>
        <line num="21" count="16" type="cond" truecount="2" falsecount="0"/>
        <line num="22" count="5" type="stmt"/>
        <line num="23" count="5" type="stmt"/>
        <line num="25" count="11" type="stmt"/>
        <line num="26" count="11" type="stmt"/>
        <line num="30" count="2" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
      </file>
      <file name="node.js" path="D:\AI\dev\Algorithms\javascript\levelwidth\node.js">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="3" count="11" type="stmt"/>
        <line num="4" count="11" type="stmt"/>
        <line num="8" count="9" type="stmt"/>
      </file>
    </package>
    <package name="javascript.linkedlist">
      <metrics statements="74" coveredstatements="73" conditionals="29" coveredconditionals="28" methods="15" coveredmethods="15"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\linkedlist\index.js">
        <metrics statements="74" coveredstatements="73" conditionals="29" coveredconditionals="28" methods="15" coveredmethods="15"/>
        <line num="7" count="68" type="stmt"/>
        <line num="8" count="68" type="stmt"/>
        <line num="14" count="27" type="stmt"/>
        <line num="18" count="26" type="stmt"/>
        <line num="22" count="11" type="stmt"/>
        <line num="23" count="11" type="stmt"/>
        <line num="25" count="11" type="stmt"/>
        <line num="26" count="16" type="stmt"/>
        <line num="27" count="16" type="stmt"/>
        <line num="30" count="11" type="stmt"/>
        <line num="34" count="6" type="stmt"/>
        <line num="38" count="41" type="cond" truecount="2" falsecount="0"/>
        <line num="39" count="10" type="stmt"/>
        <line num="42" count="31" type="stmt"/>
        <line num="43" count="31" type="stmt"/>
        <line num="44" count="57" type="cond" truecount="2" falsecount="0"/>
        <line num="45" count="31" type="stmt"/>
        <line num="47" count="26" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="56" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="3" type="stmt"/>
        <line num="64" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="65" count="1" type="stmt"/>
        <line num="68" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="1" type="stmt"/>
        <line num="73" count="2" type="stmt"/>
        <line num="74" count="2" type="stmt"/>
        <line num="75" count="2" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="79" count="2" type="stmt"/>
        <line num="83" count="36" type="stmt"/>
        <line num="85" count="36" type="cond" truecount="2" falsecount="0"/>
        <line num="87" count="26" type="stmt"/>
        <line num="90" count="10" type="stmt"/>
        <line num="95" count="40" type="stmt"/>
        <line num="96" count="40" type="stmt"/>
        <line num="97" count="40" type="stmt"/>
        <line num="98" count="91" type="cond" truecount="2" falsecount="0"/>
        <line num="99" count="37" type="stmt"/>
        <line num="102" count="54" type="stmt"/>
        <line num="103" count="54" type="stmt"/>
        <line num="105" count="3" type="stmt"/>
        <line num="109" count="7" type="cond" truecount="2" falsecount="0"/>
        <line num="110" count="3" type="stmt"/>
        <line num="113" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="118" count="3" type="stmt"/>
        <line num="119" count="3" type="cond" truecount="4" falsecount="0"/>
        <line num="120" count="1" type="stmt"/>
        <line num="122" count="2" type="stmt"/>
        <line num="126" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="127" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="131" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="132" count="1" type="stmt"/>
        <line num="133" count="1" type="stmt"/>
        <line num="136" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="137" count="3" type="stmt"/>
        <line num="138" count="3" type="stmt"/>
        <line num="142" count="1" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="4" type="stmt"/>
        <line num="146" count="4" type="stmt"/>
        <line num="147" count="4" type="stmt"/>
        <line num="152" count="2" type="stmt"/>
        <line num="153" count="2" type="stmt"/>
        <line num="154" count="4" type="stmt"/>
        <line num="155" count="4" type="stmt"/>
        <line num="160" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.matrix">
      <metrics statements="27" coveredstatements="27" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\matrix\index.js">
        <metrics statements="27" coveredstatements="27" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
        <line num="19" count="3" type="stmt"/>
        <line num="21" count="3" type="stmt"/>
        <line num="22" count="9" type="stmt"/>
        <line num="25" count="3" type="stmt"/>
        <line num="26" count="3" type="stmt"/>
        <line num="27" count="3" type="stmt"/>
        <line num="28" count="3" type="stmt"/>
        <line num="29" count="3" type="stmt"/>
        <line num="30" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="32" count="5" type="stmt"/>
        <line num="33" count="12" type="stmt"/>
        <line num="34" count="12" type="stmt"/>
        <line num="36" count="5" type="stmt"/>
        <line num="39" count="5" type="stmt"/>
        <line num="40" count="7" type="stmt"/>
        <line num="41" count="7" type="stmt"/>
        <line num="43" count="5" type="stmt"/>
        <line num="46" count="5" type="stmt"/>
        <line num="47" count="7" type="stmt"/>
        <line num="48" count="7" type="stmt"/>
        <line num="50" count="5" type="stmt"/>
        <line num="53" count="5" type="stmt"/>
        <line num="54" count="3" type="stmt"/>
        <line num="55" count="3" type="stmt"/>
        <line num="57" count="5" type="stmt"/>
        <line num="60" count="3" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.maxchar">
      <metrics statements="13" coveredstatements="13" conditionals="4" coveredconditionals="4" methods="1" coveredmethods="1"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\maxchar\index.js">
        <metrics statements="13" coveredstatements="13" conditionals="4" coveredconditionals="4" methods="1" coveredmethods="1"/>
        <line num="9" count="3" type="stmt"/>
        <line num="10" count="3" type="stmt"/>
        <line num="11" count="3" type="stmt"/>
        <line num="13" count="3" type="stmt"/>
        <line num="14" count="33" type="cond" truecount="2" falsecount="0"/>
        <line num="15" count="10" type="stmt"/>
        <line num="17" count="23" type="stmt"/>
        <line num="21" count="3" type="stmt"/>
        <line num="22" count="23" type="cond" truecount="2" falsecount="0"/>
        <line num="23" count="3" type="stmt"/>
        <line num="24" count="3" type="stmt"/>
        <line num="28" count="3" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.midpoint">
      <metrics statements="96" coveredstatements="25" conditionals="32" coveredconditionals="8" methods="16" coveredmethods="6"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\midpoint\index.js">
        <metrics statements="7" coveredstatements="7" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
        <line num="16" count="4" type="stmt"/>
        <line num="17" count="4" type="stmt"/>
        <line num="19" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="20" count="4" type="stmt"/>
        <line num="21" count="4" type="stmt"/>
        <line num="24" count="4" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
      </file>
      <file name="linkedlist.js" path="D:\AI\dev\Algorithms\javascript\midpoint\linkedlist.js">
        <metrics statements="89" coveredstatements="18" conditionals="30" coveredconditionals="6" methods="15" coveredmethods="5"/>
        <line num="3" count="14" type="stmt"/>
        <line num="4" count="14" type="stmt"/>
        <line num="10" count="4" type="stmt"/>
        <line num="12" count="4" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="125" count="8" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="133" count="14" type="cond" truecount="2" falsecount="0"/>
        <line num="134" count="4" type="stmt"/>
        <line num="137" count="10" type="stmt"/>
        <line num="138" count="10" type="stmt"/>
        <line num="139" count="10" type="stmt"/>
        <line num="142" count="10" type="stmt"/>
        <line num="146" count="14" type="stmt"/>
        <line num="148" count="14" type="cond" truecount="2" falsecount="0"/>
        <line num="149" count="10" type="stmt"/>
        <line num="150" count="10" type="stmt"/>
        <line num="152" count="4" type="stmt"/>
        <line num="153" count="4" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="159" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="178" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.palindrome">
      <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\palindrome\index.js">
        <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="11" count="7" type="stmt"/>
        <line num="12" count="23" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.pyramid">
      <metrics statements="11" coveredstatements="11" conditionals="10" coveredconditionals="10" methods="1" coveredmethods="1"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\pyramid\index.js">
        <metrics statements="11" coveredstatements="11" conditionals="10" coveredconditionals="10" methods="1" coveredmethods="1"/>
        <line num="18" count="61" type="cond" truecount="2" falsecount="0"/>
        <line num="19" count="3" type="stmt"/>
        <line num="22" count="58" type="cond" truecount="2" falsecount="0"/>
        <line num="23" count="9" type="stmt"/>
        <line num="24" count="9" type="stmt"/>
        <line num="27" count="49" type="stmt"/>
        <line num="29" count="49" type="cond" truecount="4" falsecount="0"/>
        <line num="30" count="29" type="stmt"/>
        <line num="32" count="20" type="stmt"/>
        <line num="34" count="49" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.qfroms">
      <metrics statements="22" coveredstatements="22" conditionals="0" coveredconditionals="0" methods="8" coveredmethods="8"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\qfroms\index.js">
        <metrics statements="17" coveredstatements="17" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="4"/>
        <line num="15" count="1" type="stmt"/>
        <line num="19" count="4" type="stmt"/>
        <line num="20" count="4" type="stmt"/>
        <line num="24" count="7" type="stmt"/>
        <line num="28" count="7" type="stmt"/>
        <line num="29" count="10" type="stmt"/>
        <line num="32" count="7" type="stmt"/>
        <line num="34" count="7" type="stmt"/>
        <line num="35" count="4" type="stmt"/>
        <line num="38" count="7" type="stmt"/>
        <line num="42" count="2" type="stmt"/>
        <line num="43" count="4" type="stmt"/>
        <line num="46" count="2" type="stmt"/>
        <line num="48" count="2" type="stmt"/>
        <line num="49" count="4" type="stmt"/>
        <line num="52" count="2" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
      </file>
      <file name="stack.js" path="D:\AI\dev\Algorithms\javascript\qfroms\stack.js">
        <metrics statements="5" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="4"/>
        <line num="3" count="8" type="stmt"/>
        <line num="7" count="29" type="stmt"/>
        <line num="11" count="29" type="stmt"/>
        <line num="15" count="42" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.queue">
      <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="3"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\queue\index.js">
        <metrics statements="4" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="3"/>
        <line num="13" count="3" type="stmt"/>
        <line num="17" count="5" type="stmt"/>
        <line num="21" count="5" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.reverseint">
      <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\reverseint\index.js">
        <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="12" count="9" type="stmt"/>
        <line num="18" count="9" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.reversestring">
      <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\reversestring\index.js">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="10" count="10" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.sorting">
      <metrics statements="30" coveredstatements="30" conditionals="12" coveredconditionals="12" methods="4" coveredmethods="4"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\sorting\index.js">
        <metrics statements="30" coveredstatements="30" conditionals="12" coveredconditionals="12" methods="4" coveredmethods="4"/>
        <line num="6" count="1" type="stmt"/>
        <line num="7" count="7" type="stmt"/>
        <line num="8" count="21" type="cond" truecount="2" falsecount="0"/>
        <line num="9" count="11" type="stmt"/>
        <line num="10" count="11" type="stmt"/>
        <line num="11" count="11" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
        <line num="22" count="7" type="stmt"/>
        <line num="24" count="7" type="stmt"/>
        <line num="25" count="21" type="cond" truecount="2" falsecount="0"/>
        <line num="26" count="8" type="stmt"/>
        <line num="30" count="7" type="cond" truecount="2" falsecount="0"/>
        <line num="31" count="5" type="stmt"/>
        <line num="32" count="5" type="stmt"/>
        <line num="33" count="5" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="41" count="13" type="cond" truecount="2" falsecount="0"/>
        <line num="42" count="7" type="stmt"/>
        <line num="45" count="6" type="stmt"/>
        <line num="46" count="6" type="stmt"/>
        <line num="47" count="6" type="stmt"/>
        <line num="49" count="6" type="stmt"/>
        <line num="53" count="7" type="stmt"/>
        <line num="55" count="7" type="cond" truecount="2" falsecount="0"/>
        <line num="56" count="16" type="cond" truecount="2" falsecount="0"/>
        <line num="57" count="8" type="stmt"/>
        <line num="59" count="8" type="stmt"/>
        <line num="63" count="7" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.stack">
      <metrics statements="5" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="4"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\stack\index.js">
        <metrics statements="5" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="4"/>
        <line num="15" count="3" type="stmt"/>
        <line num="19" count="8" type="stmt"/>
        <line num="23" count="8" type="stmt"/>
        <line num="27" count="3" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.steps">
      <metrics statements="8" coveredstatements="8" conditionals="8" coveredconditionals="8" methods="1" coveredmethods="1"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\steps\index.js">
        <metrics statements="8" coveredstatements="8" conditionals="8" coveredconditionals="8" methods="1" coveredmethods="1"/>
        <line num="21" count="23" type="cond" truecount="2" falsecount="0"/>
        <line num="22" count="3" type="stmt"/>
        <line num="25" count="20" type="cond" truecount="2" falsecount="0"/>
        <line num="26" count="6" type="stmt"/>
        <line num="27" count="6" type="stmt"/>
        <line num="30" count="14" type="cond" truecount="2" falsecount="0"/>
        <line num="31" count="14" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.tree">
      <metrics statements="17" coveredstatements="17" conditionals="0" coveredconditionals="0" methods="7" coveredmethods="7"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\tree\index.js">
        <metrics statements="17" coveredstatements="17" conditionals="0" coveredconditionals="0" methods="7" coveredmethods="7"/>
        <line num="15" count="13" type="stmt"/>
        <line num="16" count="13" type="stmt"/>
        <line num="20" count="8" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="32" count="3" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="38" count="4" type="stmt"/>
        <line num="40" count="4" type="stmt"/>
        <line num="41" count="4" type="stmt"/>
        <line num="46" count="1" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="48" count="4" type="stmt"/>
        <line num="50" count="4" type="stmt"/>
        <line num="51" count="4" type="stmt"/>
        <line num="56" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.validate">
      <metrics statements="22" coveredstatements="21" conditionals="30" coveredconditionals="28" methods="3" coveredmethods="3"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\validate\index.js">
        <metrics statements="10" coveredstatements="9" conditionals="18" coveredconditionals="17" methods="1" coveredmethods="1"/>
        <line num="9" count="9" type="cond" truecount="4" falsecount="0"/>
        <line num="10" count="1" type="stmt"/>
        <line num="13" count="8" type="cond" truecount="3" falsecount="1"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="8" type="cond" truecount="4" falsecount="0"/>
        <line num="18" count="2" type="stmt"/>
        <line num="21" count="6" type="cond" truecount="4" falsecount="0"/>
        <line num="22" count="1" type="stmt"/>
        <line num="25" count="5" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
      </file>
      <file name="node.js" path="D:\AI\dev\Algorithms\javascript\validate\node.js">
        <metrics statements="12" coveredstatements="12" conditionals="12" coveredconditionals="11" methods="2" coveredmethods="2"/>
        <line num="3" count="11" type="stmt"/>
        <line num="4" count="11" type="stmt"/>
        <line num="5" count="11" type="stmt"/>
        <line num="9" count="12" type="cond" truecount="4" falsecount="0"/>
        <line num="10" count="2" type="stmt"/>
        <line num="11" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="12" count="4" type="stmt"/>
        <line num="13" count="6" type="cond" truecount="4" falsecount="0"/>
        <line num="14" count="2" type="stmt"/>
        <line num="15" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="16" count="4" type="stmt"/>
        <line num="21" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.vowels">
      <metrics statements="3" coveredstatements="3" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\vowels\index.js">
        <metrics statements="3" coveredstatements="3" conditionals="2" coveredconditionals="2" methods="1" coveredmethods="1"/>
        <line num="11" count="3" type="stmt"/>
        <line num="12" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="15" count="1" type="stmt"/>
      </file>
    </package>
    <package name="javascript.weave">
      <metrics statements="14" coveredstatements="14" conditionals="6" coveredconditionals="4" methods="5" coveredmethods="5"/>
      <file name="index.js" path="D:\AI\dev\Algorithms\javascript\weave\index.js">
        <metrics statements="9" coveredstatements="9" conditionals="6" coveredconditionals="4" methods="1" coveredmethods="1"/>
        <line num="25" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="30" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="31" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="32" count="4" type="stmt"/>
        <line num="35" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="36" count="4" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
      </file>
      <file name="queue.js" path="D:\AI\dev\Algorithms\javascript\weave\queue.js">
        <metrics statements="5" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="4"/>
        <line num="9" count="5" type="stmt"/>
        <line num="13" count="18" type="stmt"/>
        <line num="17" count="19" type="stmt"/>
        <line num="21" count="16" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
