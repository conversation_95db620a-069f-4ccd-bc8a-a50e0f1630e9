name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  # JavaScript Tests
  javascript-tests:
    name: JavaScript Tests
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [16.x, 18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: javascript/package-lock.json
        
    - name: Install dependencies
      working-directory: ./javascript
      run: npm ci
      
    - name: Run ESLint
      working-directory: ./javascript
      run: npm run lint
      
    - name: Run Prettier check
      working-directory: ./javascript
      run: npm run format:check
      
    - name: Run tests
      working-directory: ./javascript
      run: npm test
      
    - name: Run tests with coverage
      working-directory: ./javascript
      run: npm run test:coverage
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./javascript/coverage/lcov.info
        flags: javascript
        name: javascript-coverage
        fail_ci_if_error: false

  # Python Tests
  python-tests:
    name: Python Tests
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        python-version: [3.8, 3.9, '3.10', '3.11']
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('python/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
          
    - name: Install dependencies
      working-directory: ./python
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        
    - name: Run Black formatter check
      working-directory: ./python
      run: black --check .
      
    - name: Run isort check
      working-directory: ./python
      run: isort --check-only .
      
    - name: Run Flake8 linter
      working-directory: ./python
      run: flake8 .
      
    - name: Run MyPy type checker
      working-directory: ./python
      run: mypy . --ignore-missing-imports
      continue-on-error: true  # MyPy might fail on some files
      
    - name: Run tests
      working-directory: ./python
      run: pytest
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./python/coverage.xml
        flags: python
        name: python-coverage
        fail_ci_if_error: false

  # Security Scan
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # Documentation Check
  docs-check:
    name: Documentation Check
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Check README links
      uses: gaurav-nelson/github-action-markdown-link-check@v1
      with:
        use-quiet-mode: 'yes'
        use-verbose-mode: 'yes'
        config-file: '.github/markdown-link-check-config.json'
        
    - name: Lint markdown files
      uses: DavidAnson/markdownlint-cli2-action@v13
      with:
        globs: '**/*.md'

  # Build and Test Matrix
  build-matrix:
    name: Build Matrix Test
    runs-on: ${{ matrix.os }}
    
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
        cache-dependency-path: javascript/package-lock.json
        
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
        
    - name: Test JavaScript on ${{ matrix.os }}
      working-directory: ./javascript
      run: |
        npm ci
        npm test
        
    - name: Test Python on ${{ matrix.os }}
      working-directory: ./python
      run: |
        python -m pip install --upgrade pip
        pip install pytest
        python -m pytest tests/ -v || echo "Python tests completed"

  # Performance Benchmarks
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        cache: 'npm'
        cache-dependency-path: javascript/package-lock.json
        
    - name: Install dependencies
      working-directory: ./javascript
      run: npm ci
      
    - name: Run performance benchmarks
      working-directory: ./javascript
      run: |
        echo "Running performance benchmarks..."
        # Add actual benchmark commands here
        echo "Benchmarks completed"

  # Deployment (only on main branch)
  deploy:
    name: Deploy Documentation
    runs-on: ubuntu-latest
    needs: [javascript-tests, python-tests, docs-check]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18.x'
        
    - name: Generate documentation
      run: |
        echo "Generating documentation..."
        # Add documentation generation commands here
        
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      if: success()
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./docs
        publish_branch: gh-pages
