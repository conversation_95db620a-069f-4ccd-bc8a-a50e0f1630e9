# eslint-config-prettier

Turns off all rules that are unnecessary or might conflict with [Prettier].

This lets you use your favorite shareable config without letting its stylistic choices get in the way when using Prettier.

Note that this config _only_ turns rules _off,_ so it only makes sense using it together with some other config.

[prettier]: https://github.com/prettier/prettier

**[➡️ Full readme](https://github.com/prettier/eslint-config-prettier/)**