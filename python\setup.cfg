[metadata]
name = algorithms-collection-python
version = 1.0.0
description = Comprehensive collection of algorithms and data structures implemented in Python
long_description = file: README.md
long_description_content_type = text/markdown
author = Algorithms Collection Contributors
author_email = <EMAIL>
url = https://github.com/your-username/algorithms
project_urls =
    Bug Reports = https://github.com/your-username/algorithms/issues
    Source = https://github.com/your-username/algorithms
classifiers =
    Development Status :: 4 - Beta
    Intended Audience :: Developers
    Intended Audience :: Education
    License :: OSI Approved :: MIT License
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.7
    Programming Language :: Python :: 3.8
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Topic :: Software Development :: Libraries :: Python Modules
    Topic :: Education
    Topic :: Scientific/Engineering

[options]
packages = find:
python_requires = >=3.7
include_package_data = True

[options.packages.find]
exclude =
    tests*
    docs*

# Flake8 configuration
[flake8]
max-line-length = 88
extend-ignore = E203, W503, E501
exclude =
    .git,
    __pycache__,
    .pytest_cache,
    .coverage,
    *.egg-info,
    build,
    dist

# MyPy configuration
[mypy]
python_version = 3.7
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
strict_equality = True

# Pytest configuration
[tool:pytest]
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=.
    --cov-report=term-missing
    --cov-report=html
    --cov-report=xml
    --cov-fail-under=80
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests

# Coverage configuration
[coverage:run]
source = .
omit = 
    */tests/*
    */test_*
    setup.py
    */venv/*
    */virtualenv/*
    */.pytest_cache/*
    */node_modules/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

# isort configuration
[isort]
profile = black
multi_line_output = 3
line_length = 88
known_first_party = algorithms
known_third_party = pytest,numpy,matplotlib,networkx
