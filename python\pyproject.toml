[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "algorithms-collection-python"
version = "1.0.0"
description = "Comprehensive collection of algorithms and data structures implemented in Python"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Algorithms Collection Contributors", email = "<EMAIL>"}
]
maintainers = [
    {name = "Algorithms Collection Contributors", email = "<EMAIL>"}
]
keywords = [
    "algorithms",
    "data-structures", 
    "python",
    "computer-science",
    "interview-preparation",
    "leetcode",
    "sorting",
    "searching",
    "trees",
    "graphs",
    "dynamic-programming"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Education", 
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8", 
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Education",
    "Topic :: Scientific/Engineering"
]
requires-python = ">=3.7"
dependencies = [
    "numpy>=1.21.0",
    "matplotlib>=3.5.0",
    "networkx>=2.8.0"
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=22.0.0",
    "flake8>=5.0.0",
    "mypy>=1.0.0",
    "isort>=5.10.0",
    "pre-commit>=2.20.0"
]
docs = [
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.0.0"
]
jupyter = [
    "jupyter>=1.0.0",
    "ipykernel>=6.0.0"
]

[project.urls]
Homepage = "https://github.com/your-username/algorithms"
Documentation = "https://github.com/your-username/algorithms#readme"
Repository = "https://github.com/your-username/algorithms.git"
"Bug Reports" = "https://github.com/your-username/algorithms/issues"
Changelog = "https://github.com/your-username/algorithms/blob/main/CHANGELOG.md"

# Black configuration
[tool.black]
line-length = 88
target-version = ['py37', 'py38', 'py39', 'py310', 'py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.pytest_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration  
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["algorithms"]
known_third_party = ["pytest", "numpy", "matplotlib", "networkx"]

# MyPy configuration
[tool.mypy]
python_version = "3.7"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

# Pytest configuration
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config", 
    "--verbose",
    "--tb=short",
    "--cov=.",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=80"
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests"
]

# Coverage configuration
[tool.coverage.run]
source = ["."]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
    "*/venv/*", 
    "*/virtualenv/*",
    "*/.pytest_cache/*",
    "*/node_modules/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError", 
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod"
]
