# 🤝 Посібник з внеску в проект

Дякуємо за ваш інтерес до внеску в проект Algorithms! Цей документ містить інструкції та рекомендації для контрибуторів.

## 📋 Зміст

- [Кодекс поведінки](#кодекс-поведінки)
- [Як почати](#як-почати)
- [Типи внесків](#типи-внесків)
- [Процес розробки](#процес-розробки)
- [Стандарти коду](#стандарти-коду)
- [Тестування](#тестування)
- [Документація](#документація)
- [Pull Request процес](#pull-request-процес)

## 🤝 Кодекс поведінки

Цей проект дотримується [Contributor Covenant](https://www.contributor-covenant.org/). Беручи участь, ви погоджуєтеся дотримуватися цього кодексу.

## 🚀 Як почати

### 1. Налаштування середовища

```bash
# Форкніть репозиторій на GitHub
# Клонуйте ваш форк
git clone https://github.com/your-username/algorithms.git
cd algorithms

# Додайте upstream remote
git remote add upstream https://github.com/original-owner/algorithms.git

# Встановіть залежності
cd javascript && npm install
cd ../python && pip install -r requirements.txt
```

### 2. Створення гілки

```bash
# Оновіть main гілку
git checkout main
git pull upstream main

# Створіть нову гілку для вашої функції
git checkout -b feature/algorithm-name
# або
git checkout -b fix/issue-description
```

## 🎯 Типи внесків

### ✅ Що ми вітаємо:
- **Нові алгоритми** з повною реалізацією та тестами
- **Виправлення багів** в існуючому коді
- **Покращення продуктивності** алгоритмів
- **Додавання тестів** для існуючих алгоритмів
- **Покращення документації** та коментарів
- **Переклади** документації
- **Візуалізації** алгоритмів

### ❌ Що ми не приймаємо:
- Неповні реалізації без тестів
- Код без коментарів та документації
- Дублікати існуючих алгоритмів без покращень
- Код, що не відповідає стандартам проекту

## 🔄 Процес розробки

### Додавання нового алгоритму

#### JavaScript
```bash
# Створіть папку для алгоритму
mkdir javascript/algorithm-name
cd javascript/algorithm-name

# Створіть файли
touch index.js test.js
```

**Структура index.js:**
```javascript
// --- Directions
// Опис алгоритму та його призначення
// --- Examples
//   algorithmName(input) --> expected output

function algorithmName(input) {
  // Ваша реалізація тут
}

module.exports = algorithmName;
```

**Структура test.js:**
```javascript
const algorithmName = require('./index');

test('algorithm function exists', () => {
  expect(typeof algorithmName).toEqual('function');
});

test('handles basic case', () => {
  expect(algorithmName(input)).toEqual(expectedOutput);
});

// Додайте більше тестів для edge cases
```

#### Python
```python
"""
Назва алгоритму

Опис алгоритму та його призначення.

Time Complexity: O(?)
Space Complexity: O(?)

Examples:
    >>> algorithm_name(input)
    expected_output
"""

def algorithm_name(input):
    """
    Детальний опис функції.
    
    Args:
        input: Опис параметра
        
    Returns:
        Опис повертаємого значення
        
    Raises:
        ValueError: Коли виникає помилка
    """
    # Ваша реалізація тут
    pass

if __name__ == "__main__":
    # Приклади використання
    print(algorithm_name(test_input))
```

## 📏 Стандарти коду

### JavaScript
- Використовуйте ES6+ синтаксис
- Дотримуйтесь ESLint правил
- Використовуйте camelCase для змінних та функцій
- Додавайте JSDoc коментарі для функцій

### Python
- Дотримуйтесь PEP 8
- Використовуйте type hints
- Додавайте docstrings для всіх функцій
- Використовуйте snake_case для змінних та функцій

### Загальні правила
- Код має бути читабельним та зрозумілим
- Додавайте коментарі для складних частин
- Уникайте магічних чисел
- Використовуйте описові назви змінних

## 🧪 Тестування

### JavaScript тести
```bash
# Запуск всіх тестів
npm test

# Запуск конкретного тесту
npm test algorithm-name

# Запуск з покриттям
npm test -- --coverage
```

### Python тести
```bash
# Запуск всіх тестів
pytest

# Запуск конкретного файлу
pytest test_algorithm.py

# Запуск з покриттям
pytest --cov=.
```

### Вимоги до тестів
- Мінімум 90% покриття коду
- Тести для edge cases
- Тести для невалідних входів
- Performance тести для великих даних

## 📖 Документація

### Обов'язкові елементи:
- Опис алгоритму та його призначення
- Часова та просторова складність (Big O)
- Приклади використання
- Пояснення підходу
- Посилання на джерела (якщо є)

### Формат коментарів:
```javascript
// --- Directions
// Детальний опис того, що робить алгоритм
// --- Examples
//   input -> output
//   input2 -> output2
// --- Complexity
//   Time: O(n)
//   Space: O(1)
```

## 🔀 Pull Request процес

### 1. Перед створенням PR
- [ ] Код пройшов всі тести
- [ ] Додано нові тести для нової функціональності
- [ ] Оновлено документацію
- [ ] Код відформатовано згідно стандартів
- [ ] Немає конфліктів з main гілкою

### 2. Створення PR
- Використовуйте описову назву
- Заповніть шаблон PR
- Додайте скріншоти (якщо потрібно)
- Позначте відповідні labels

### 3. Шаблон PR
```markdown
## Опис
Короткий опис змін

## Тип змін
- [ ] Новий алгоритм
- [ ] Виправлення бага
- [ ] Покращення продуктивності
- [ ] Оновлення документації

## Тестування
- [ ] Всі тести проходять
- [ ] Додано нові тести
- [ ] Перевірено на edge cases

## Checklist
- [ ] Код відповідає стандартам проекту
- [ ] Додано/оновлено документацію
- [ ] Немає breaking changes
```

## 🏷️ Система міток

- `good first issue` - Підходить для новачків
- `help wanted` - Потрібна допомога спільноти
- `bug` - Виправлення помилки
- `enhancement` - Нова функціональність
- `documentation` - Покращення документації
- `performance` - Оптимізація продуктивності

## 🆘 Отримання допомоги

- Створіть issue для обговорення
- Приєднайтесь до Discussions
- Зверніться до мейнтейнерів
- Перегляньте існуючі PR для прикладів

## 🎉 Визнання

Всі контрибутори будуть додані до списку в README.md. Дякуємо за ваш внесок!

---

**Пам'ятайте: Немає занадто маленького внеску. Кожна допомога цінується!** 🙏
