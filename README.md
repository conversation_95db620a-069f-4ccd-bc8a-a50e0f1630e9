# 🚀 Algorithms & Data Structures Collection

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![JavaScript](https://img.shields.io/badge/JavaScript-ES6+-yellow.svg)](https://developer.mozilla.org/en-US/docs/Web/JavaScript)
[![Python](https://img.shields.io/badge/Python-3.7+-blue.svg)](https://www.python.org/)
[![Tests](https://img.shields.io/badge/Tests-Jest%20%7C%20PyTest-green.svg)](https://jestjs.io/)

Комплексна колекція алгоритмів та структур даних, реалізованих на JavaScript та Python з детальними поясненнями, тестами та прикладами використання.

## 📋 Зміст

- [Огляд проекту](#огляд-проекту)
- [Швидкий старт](#швидкий-старт)
- [Структура проекту](#структура-проекту)
- [Категорії алгоритмів](#категорії-алгоритмів)
- [Встановлення](#встановлення)
- [Запуск тестів](#запуск-тестів)
- [Внесок у проект](#внесок-у-проект)
- [Ліцензія](#ліцензія)

## 🎯 Огляд проекту

Цей репозиторій містить:

- **32+ JavaScript алгоритми** з повним тестовим покриттям
- **500+ Python алгоритми** включаючи рішення LeetCode задач
- Детальні пояснення складності алгоритмів (Big O)
- Інтерактивні приклади та візуалізації
- Порівняльний аналіз різних підходів

### 🎓 Для кого цей проект?

- Студенти, що вивчають алгоритми та структури даних
- Розробники, що готуються до технічних співбесід
- Викладачі та ментори
- Всі, хто хоче поглибити знання в Computer Science

## ⚡ Швидкий старт

### JavaScript

```bash
# Клонування репозиторію
git clone https://github.com/your-username/algorithms.git
cd algorithms/javascript

# Встановлення залежностей
npm install

# Запуск тестів
npm test

# Запуск конкретного тесту
npm test anagrams
```

### Python

```bash
# Перехід до Python директорії
cd python

# Встановлення залежностей
pip install -r requirements.txt

# Запуск тестів
pytest

# Запуск конкретного файлу
python BinarySearch.py
```

## 📁 Структура проекту

```
algorithms/
├── javascript/                 # JavaScript реалізації
│   ├── anagrams/              # Перевірка анаграм
│   ├── sorting/               # Алгоритми сортування
│   ├── tree/                  # Дерева та обходи
│   ├── linkedlist/            # Зв'язані списки
│   └── ...                    # Інші алгоритми
├── python/                    # Python реалізації
│   ├── BinarySearch.py        # Бінарний пошук
│   ├── Fibonacci.py           # Числа Фібоначчі
│   ├── sorting/               # Алгоритми сортування
│   └── ...                    # 500+ алгоритмів
├── docs/                      # Документація
├── tests/                     # Додаткові тести
└── README.md                  # Цей файл
```

## 🗂️ Категорії алгоритмів

### 🔤 Робота з рядками

- **Anagrams** - Перевірка анаграм
- **Palindrome** - Перевірка паліндромів
- **Reverse String** - Реверс рядка
- **Capitalize** - Капіталізація слів
- **Max Character** - Найчастіший символ

### 🔢 Математичні алгоритми

- **Fibonacci** - Числа Фібоначчі (рекурсія + мемоізація)
- **FizzBuzz** - Класична задача FizzBuzz
- **Reverse Integer** - Реверс числа
- **Steps/Pyramid** - Побудова піраміди

### 📊 Сортування та пошук

- **Bubble Sort** - Бульбашкове сортування O(n²)
- **Selection Sort** - Сортування вибором O(n²)
- **Merge Sort** - Сортування злиттям O(n log n)
- **Binary Search** - Бінарний пошук O(log n)

### 🔗 Структури даних

- **Linked Lists** - Зв'язані списки
- **Stacks** - Стеки
- **Queues** - Черги
- **Trees** - Дерева (BFS, DFS)
- **Binary Search Trees** - Бінарні дерева пошуку

### 🌐 Графи та дерева

- **Tree Traversal** - Обходи дерев
- **Level Width** - Ширина рівнів дерева
- **Validate BST** - Валідація BST
- **Graph Algorithms** - Алгоритми на графах

### 🧮 Динамічне програмування

- **Fibonacci DP** - Фібоначчі з мемоізацією
- **Climbing Stairs** - Підйом по сходах
- **Coin Change** - Розмін монет
- **Longest Subsequence** - Найдовша підпослідовність

## 🛠️ Встановлення

### Системні вимоги

- **Node.js** 14+ для JavaScript
- **Python** 3.7+ для Python
- **Git** для клонування репозиторію

### Крок за кроком

1. **Клонування репозиторію**

```bash
git clone https://github.com/your-username/algorithms.git
cd algorithms
```

2. **JavaScript налаштування**

```bash
cd javascript
npm install
npm test
```

3. **Python налаштування**

```bash
cd ../python
pip install -r requirements.txt
pytest
```

## 🧪 Запуск тестів

### JavaScript тести

```bash
# Всі тести
npm test

# Конкретний алгоритм
npm test anagrams
npm test sorting
npm test tree

# З детальним виводом
npm test -- --verbose
```

### Python тести

```bash
# Всі тести
pytest

# Конкретний файл
pytest test_binary_search.py

# З покриттям коду
pytest --cov=.
```

## 📚 Приклади використання

### JavaScript приклад

```javascript
const anagrams = require('./anagrams');
const { bubbleSort } = require('./sorting');

// Перевірка анаграм
console.log(anagrams('listen', 'silent')); // true

// Сортування масиву
const arr = [64, 34, 25, 12, 22, 11, 90];
console.log(bubbleSort(arr)); // [11, 12, 22, 25, 34, 64, 90]
```

### Python приклад

```python
from BinarySearch import binarySearch
from Fibonacci import fib

# Бінарний пошук
arr = [1, 3, 5, 7, 9, 11]
result = binarySearch(arr, 0, len(arr)-1, 7)
print(f"Елемент знайдено на позиції: {result}")

# Числа Фібоначчі
print(f"10-те число Фібоначчі: {fib(10)}")
```

## 🤝 Внесок у проект

Ми вітаємо внески від спільноти! Будь ласка, ознайомтеся з [CONTRIBUTING.md](CONTRIBUTING.md) для детальних інструкцій.

### Як допомогти

1. 🐛 **Знайшли баг?** Створіть issue
2. 💡 **Маєте ідею?** Обговоріть в Discussions
3. 🔧 **Хочете додати алгоритм?** Створіть Pull Request
4. 📖 **Покращити документацію?** Завжди вітається!

### Швидкий старт для контрибуторів

```bash
# Форкніть репозиторій
git clone https://github.com/your-username/algorithms.git
cd algorithms

# Створіть нову гілку
git checkout -b feature/new-algorithm

# Внесіть зміни та додайте тести
# ...

# Закомітьте зміни
git commit -m "Add: новий алгоритм сортування"

# Відправте зміни
git push origin feature/new-algorithm

# Створіть Pull Request
```

## 📊 Статистика проекту

- **JavaScript**: 32 алгоритми з тестами
- **Python**: 500+ алгоритмів та рішень
- **Тестове покриття**: 95%+
- **Підтримувані мови**: JavaScript, Python
- **Планується**: Go, Rust, Java

## 🔗 Корисні посилання

- [LeetCode](https://leetcode.com/) - Платформа для практики
- [HackerRank](https://www.hackerrank.com/) - Coding challenges
- [GeeksforGeeks](https://www.geeksforgeeks.org/) - Алгоритми та структури даних
- [Visualgo](https://visualgo.net/) - Візуалізація алгоритмів
- [Big O Cheat Sheet](https://www.bigocheatsheet.com/) - Довідник складності

## 📄 Ліцензія

Цей проект ліцензовано під [MIT License](LICENSE) - дивіться файл LICENSE для деталей.

## 🙏 Подяки

- Спасибі всім контрибуторам за їхній внесок
- Натхнення від спільноти алгоритмів
- Особлива подяка викладачам Computer Science

---

**⭐ Якщо цей проект був корисним, поставте зірочку!**

Створено з ❤️ для спільноти розробників
